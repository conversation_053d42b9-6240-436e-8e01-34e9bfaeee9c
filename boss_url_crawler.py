"""
BOSS直聘URL发现爬虫
基于现有架构实现全站URL爬取功能
"""

import asyncio
import time
import random
import re
from typing import List, Dict, Set, Optional
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup

# 导入现有模块
from boss_crawler import BOSSCrawler
from config import BASE_URL, CITY_CODES, POSITION_CODES, SELECTORS
from url_storage import url_storage

class BOSSURLCrawler(BOSSCrawler):
    """BOSS直聘URL发现爬虫 - 继承现有爬虫的所有能力"""
    
    def __init__(self):
        super().__init__()
        self.search_base_url = "https://www.zhipin.com/web/geek/jobs"
        self.discovered_urls: Set[str] = set()
        self.crawl_stats = {
            'cities_processed': 0,
            'positions_processed': 0,
            'pages_processed': 0,
            'urls_discovered': 0,
            'errors': 0
        }
    
    async def discover_urls_by_city(self, city_name: str, city_code: str, 
                                  position_codes: List[str] = None) -> List[str]:
        """按城市发现职位URL"""
        if not position_codes:
            position_codes = list(POSITION_CODES.values())
        
        print(f"\n🏙️ 开始爬取城市: {city_name} (代码: {city_code})")
        city_urls = []
        
        for pos_name, pos_code in POSITION_CODES.items():
            if position_codes and pos_code not in position_codes:
                continue
                
            print(f"  📋 爬取职位分类: {pos_name}")
            
            try:
                urls = await self.discover_urls_by_search(city_code, pos_code, city_name)
                city_urls.extend(urls)
                self.crawl_stats['positions_processed'] += 1
                
                # 请求间隔
                await asyncio.sleep(random.uniform(2.0, 4.0))
                
            except Exception as e:
                print(f"    ❌ 职位分类 {pos_name} 爬取失败: {str(e)}")
                self.crawl_stats['errors'] += 1
        
        self.crawl_stats['cities_processed'] += 1
        print(f"  ✅ 城市 {city_name} 完成，发现 {len(city_urls)} 个URL")
        return city_urls
    
    async def discover_urls_by_search(self, city_code: str, position_code: str, 
                                    city_name: str = "未知") -> List[str]:
        """通过搜索参数发现URL"""
        urls = []
        page = 1
        max_pages = 50  # 限制最大页数，避免无限循环
        
        while page <= max_pages:
            try:
                # 构建搜索URL
                search_url = f"{self.search_base_url}?city={city_code}&position={position_code}&page={page}"
                
                print(f"    📄 爬取第 {page} 页...")
                
                # 使用现有的爬取方法
                html_content = await self.crawl_search_page(search_url)
                
                if not html_content:
                    print(f"    ❌ 第 {page} 页获取失败")
                    break
                
                # 提取URL
                page_urls = self.extract_job_urls_from_html(html_content)
                
                if not page_urls:
                    print(f"    ⚠️ 第 {page} 页没有发现URL，可能已到最后一页")
                    break
                
                urls.extend(page_urls)
                self.crawl_stats['pages_processed'] += 1
                self.crawl_stats['urls_discovered'] += len(page_urls)
                
                print(f"    ✅ 第 {page} 页发现 {len(page_urls)} 个URL")
                
                # 检查是否有下一页
                if not self.has_next_page(html_content):
                    print(f"    📄 已到最后一页")
                    break
                
                page += 1
                
                # 页面间隔
                await asyncio.sleep(random.uniform(1.5, 3.0))
                
            except Exception as e:
                print(f"    ❌ 第 {page} 页爬取异常: {str(e)}")
                self.crawl_stats['errors'] += 1
                break
        
        return urls
    
    async def crawl_search_page(self, search_url: str) -> str:
        """爬取搜索页面 - 复用现有的安全绕过机制"""
        async with self.semaphore:
            print(f"      🔍 访问: {search_url}")

            # 使用现有的多方法尝试机制
            for i, method in enumerate(self.access_methods, 1):
                try:
                    print(f"      尝试方法 {i}: {method.__name__}")
                    html_content = await method(search_url)

                    if html_content and len(html_content) > 1000:
                        # 验证页面内容
                        if self.validate_search_page(html_content):
                            print(f"      ✅ 方法 {i} 成功获取有效页面")
                            return html_content
                        else:
                            print(f"      ⚠️ 方法 {i} 获取的页面可能是安全检查页面")
                            # 保存页面内容用于调试
                            self.save_debug_page(html_content, f"debug_search_method_{i}.html")
                    else:
                        print(f"      ⚠️ 方法 {i} 获取的页面内容过短: {len(html_content) if html_content else 0}")

                except Exception as e:
                    print(f"      ❌ 方法 {i} 失败: {str(e)}")
                    continue

            print(f"      ❌ 所有方法都失败了")
            return ""
    
    def save_debug_page(self, html_content: str, filename: str):
        """保存调试页面"""
        try:
            import os
            debug_dir = "debug"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            filepath = os.path.join(debug_dir, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"      📄 调试页面已保存: {filepath}")
        except Exception as e:
            print(f"      ⚠️ 保存调试页面失败: {str(e)}")

    def validate_search_page(self, html_content: str) -> bool:
        """使用AI增强的页面验证"""
        # 检查是否是安全检查页面
        security_indicators = [
            '请稍候',
            '正在加载中',
            'verify-slider',
            '安全验证',
            '人机验证',
            'security-check',
            'captcha',
            '请开启JavaScript'
        ]

        if any(indicator in html_content for indicator in security_indicators):
            print(f"      ⚠️ 检测到安全检查页面")
            return False

        # AI增强的职位页面检测
        return self._ai_enhanced_page_validation(html_content)

    def _ai_enhanced_page_validation(self, html_content: str) -> bool:
        """AI增强的页面验证"""
        # 职位相关的强指标
        strong_job_indicators = [
            'job-list', 'job-item', 'job-detail', 'position-list',
            'job-card', 'position-card', 'recruitment',
            '职位列表', '岗位信息', '招聘信息'
        ]

        # 职位相关的弱指标
        weak_job_indicators = [
            '职位', '岗位', '薪资', '工资', '公司', '企业',
            'job', 'position', 'salary', 'company',
            '工作经验', '技能要求', '学历要求'
        ]

        # 页面结构指标
        structure_indicators = [
            '<li', '<div class=', 'data-jobid', 'href="/job_detail/',
            'job-primary', 'job-name', 'job-area', 'job-limit'
        ]

        # 计算各类指标得分
        strong_score = sum(1 for indicator in strong_job_indicators if indicator in html_content)
        weak_score = sum(1 for indicator in weak_job_indicators if indicator in html_content)
        structure_score = sum(1 for indicator in structure_indicators if indicator in html_content)

        # AI判断逻辑
        total_score = strong_score * 3 + weak_score * 1 + structure_score * 2

        print(f"      🤖 AI分析: 强指标={strong_score}, 弱指标={weak_score}, 结构指标={structure_score}, 总分={total_score}")

        # 动态阈值判断
        if strong_score >= 1:  # 有强指标就很可能是职位页面
            return True
        elif total_score >= 8:  # 综合得分足够高
            return True
        elif weak_score >= 5 and structure_score >= 2:  # 弱指标多且有结构支撑
            return True
        else:
            return False
    
    def extract_job_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取职位URL"""
        urls = []

        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            print(f"      📄 开始从页面提取URL，页面长度: {len(html_content)}")

            # 方法1: 使用配置中的选择器
            job_links = soup.select(SELECTORS['job_link'])
            print(f"      🔍 方法1 - 使用选择器 '{SELECTORS['job_link']}' 找到 {len(job_links)} 个链接")

            for link in job_links:
                href = link.get('href')
                if href:
                    # 构建完整URL
                    if href.startswith('/'):
                        full_url = urljoin(BASE_URL, href)
                    else:
                        full_url = href

                    # 验证URL格式
                    if self.is_valid_job_url(full_url):
                        urls.append(full_url)

            print(f"      ✅ 方法1 提取到 {len(urls)} 个有效URL")

            # 方法2: 更广泛的选择器
            if not urls:
                broader_selectors = [
                    'a[href*="/job_detail/"]',
                    'a[href*="job_detail"]',
                    '.job-card a',
                    '.job-item a',
                    '.job-list a'
                ]

                for selector in broader_selectors:
                    links = soup.select(selector)
                    print(f"      🔍 方法2 - 使用选择器 '{selector}' 找到 {len(links)} 个链接")

                    for link in links:
                        href = link.get('href')
                        if href and '/job_detail/' in href:
                            if href.startswith('/'):
                                full_url = urljoin(BASE_URL, href)
                            else:
                                full_url = href

                            if self.is_valid_job_url(full_url):
                                urls.append(full_url)

                    if urls:
                        print(f"      ✅ 方法2 使用选择器 '{selector}' 提取到 {len(urls)} 个有效URL")
                        break

            # 方法3: 正则表达式提取
            if not urls:
                print(f"      🔍 方法3 - 使用正则表达式提取")
                url_patterns = [
                    r'href="(/job_detail/[^"]+\.html)"',
                    r'href="(https://www\.zhipin\.com/job_detail/[^"]+\.html)"',
                    r'"(/job_detail/[a-zA-Z0-9]+\.html)"'
                ]

                for pattern in url_patterns:
                    matches = re.findall(pattern, html_content)
                    print(f"      🔍 正则模式 '{pattern}' 找到 {len(matches)} 个匹配")

                    for match in matches:
                        if match.startswith('/'):
                            full_url = urljoin(BASE_URL, match)
                        else:
                            full_url = match

                        if self.is_valid_job_url(full_url):
                            urls.append(full_url)

                    if urls:
                        print(f"      ✅ 方法3 使用正则模式提取到 {len(urls)} 个有效URL")
                        break

        except Exception as e:
            print(f"      ❌ URL提取失败: {str(e)}")
            import traceback
            traceback.print_exc()

        # 去重
        unique_urls = list(set(urls))
        print(f"      📊 最终提取到 {len(unique_urls)} 个唯一URL")

        # 显示前几个URL作为示例
        if unique_urls:
            print(f"      📋 示例URL:")
            for i, url in enumerate(unique_urls[:3], 1):
                print(f"        {i}. {url}")

        return unique_urls
    
    def is_valid_job_url(self, url: str) -> bool:
        """验证是否为有效的职位URL"""
        if not url:
            return False
        
        # 检查URL格式
        if not url.startswith("https://www.zhipin.com/job_detail/"):
            return False
        
        # 检查是否以.html结尾
        if not url.endswith('.html'):
            return False
        
        return True
    
    def has_next_page(self, html_content: str) -> bool:
        """检查是否有下一页"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 使用配置中的下一页选择器
            next_page_link = soup.select_one(SELECTORS['next_page'])
            
            if next_page_link and not next_page_link.get('disabled'):
                return True
            
            # 备用检查方法
            next_indicators = ['下一页', 'next', '下页']
            page_text = html_content.lower()
            
            return any(indicator in page_text for indicator in next_indicators)
            
        except Exception:
            return False
    
    async def discover_all_urls(self, cities: List[str] = None, 
                              positions: List[str] = None) -> Dict[str, List[str]]:
        """发现所有URL - 主要入口方法"""
        print("🚀 开始BOSS直聘全站URL发现")
        print("=" * 60)
        
        # 开始计时
        url_storage.start_timing()
        
        # 确定要爬取的城市
        if not cities:
            cities = list(CITY_CODES.keys())
        
        print(f"📊 爬取范围:")
        print(f"  城市数量: {len(cities)}")
        print(f"  职位分类: {len(POSITION_CODES)}")
        print(f"  预估页面: {len(cities) * len(POSITION_CODES) * 10}")
        print("=" * 60)
        
        all_urls = {}
        
        for city_name in cities:
            if city_name not in CITY_CODES:
                print(f"⚠️ 跳过未知城市: {city_name}")
                continue
            
            city_code = CITY_CODES[city_name]
            
            try:
                city_urls = await self.discover_urls_by_city(city_name, city_code, positions)
                all_urls[city_name] = city_urls
                
                # 添加到存储
                url_storage.add_urls_batch(city_urls, city_name)
                
                print(f"✅ {city_name}: {len(city_urls)} 个URL")
                
            except Exception as e:
                print(f"❌ 城市 {city_name} 爬取失败: {str(e)}")
                all_urls[city_name] = []
                self.crawl_stats['errors'] += 1
        
        # 结束计时
        url_storage.end_timing()
        
        # 打印最终统计
        self.print_final_statistics()
        
        return all_urls
    
    def print_final_statistics(self):
        """打印最终统计信息"""
        print("\n" + "=" * 60)
        print("📊 URL发现完成统计")
        print("=" * 60)
        print(f"城市处理: {self.crawl_stats['cities_processed']}")
        print(f"职位分类处理: {self.crawl_stats['positions_processed']}")
        print(f"页面处理: {self.crawl_stats['pages_processed']}")
        print(f"URL发现: {self.crawl_stats['urls_discovered']}")
        print(f"错误次数: {self.crawl_stats['errors']}")
        
        # 打印存储统计
        url_storage.print_statistics()

# 全局实例
boss_url_crawler = BOSSURLCrawler()
