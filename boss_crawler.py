"""
BOSS直聘简化爬虫
专门用于URL发现的简化版本
"""

import asyncio
import time
import random
from typing import List, Dict, Any
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

from config import BASE_URL

# 导入BOSS安全检查绕过器
try:
    from boss_security_bypass import boss_security_bypass
    BOSS_SECURITY_BYPASS_AVAILABLE = True
    print("🚀 BOSS安全检查绕过器已加载")
except ImportError as e:
    print(f"❌ BOSS安全检查绕过器导入失败: {e}")
    BOSS_SECURITY_BYPASS_AVAILABLE = False

class BOSSCrawler:
    """BOSS直聘简化爬虫 - 专门用于URL发现"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.crawler = None
        self.semaphore = asyncio.Semaphore(5)
        self.max_retries = 3
        self.retry_delay = 2.0
        
        # 尝试不同的访问策略
        self.access_methods = []
        
        # 添加BOSS安全检查绕过方法
        if BOSS_SECURITY_BYPASS_AVAILABLE:
            self.access_methods.append(self._method_boss_wait_token)
            self.access_methods.append(self._method_boss_simulate_flow)
            self.access_methods.append(self._method_boss_direct_bypass)
        
        # 添加基础访问方法
        self.access_methods.extend([
            self._method_mcp_browser,  # 新增MCP浏览器方法
            self._method_direct_access,
            self._method_with_referrer,
            self._method_mobile_ua,
            self._method_api_approach
        ])
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        print("BOSS直聘爬虫已启动")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        print("BOSS直聘爬虫已关闭")
    
    async def _method_boss_wait_token(self, url: str) -> str:
        """方法1: 使用BOSS安全检查绕过器 - 等待token"""
        try:
            return await boss_security_bypass.method_wait_and_extract_token(url)
        except Exception as e:
            print(f"BOSS等待token方法失败: {str(e)}")
            return ""
    
    async def _method_boss_simulate_flow(self, url: str) -> str:
        """方法2: 使用BOSS安全检查绕过器 - 模拟流程"""
        try:
            return await boss_security_bypass.method_simulate_security_flow(url)
        except Exception as e:
            print(f"BOSS模拟流程方法失败: {str(e)}")
            return ""
    
    async def _method_boss_direct_bypass(self, url: str) -> str:
        """方法3: 使用BOSS安全检查绕过器 - 直接绕过"""
        try:
            return await boss_security_bypass.method_direct_bypass(url)
        except Exception as e:
            print(f"BOSS直接绕过方法失败: {str(e)}")
            return ""

    async def _method_mcp_browser(self, url: str) -> str:
        """方法1: 使用MCP浏览器工具 - AI驱动的智能反爬虫"""
        try:
            print(f"🤖 使用MCP浏览器访问: {url}")

            # 创建一个智能的浏览器访问策略
            # 模拟真实用户行为来绕过反爬虫检测

            # 策略1: 使用随机延时和真实浏览器行为
            import random
            await asyncio.sleep(random.uniform(1, 3))

            # 策略2: 使用crawl4ai的高级配置
            from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

            browser_config = BrowserConfig(
                headless=False,  # 使用有头模式，更像真实用户
                browser_type="chromium",
                viewport_width=1920,
                viewport_height=1080,
                user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )

            crawler_config = CrawlerRunConfig(
                wait_for="networkidle",  # 等待网络空闲
                delay_before_return_html=3,  # 延时3秒再返回HTML
                js_code=[
                    "window.scrollTo(0, 100);",  # 模拟滚动
                    "await new Promise(resolve => setTimeout(resolve, 2000));"  # 等待2秒
                ]
            )

            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=url, config=crawler_config)

                if result.success and result.html:
                    # 使用AI分析页面内容
                    if self._ai_analyze_page_content(result.html):
                        print("✅ AI分析：页面内容正常，包含职位信息")
                        return result.html
                    else:
                        print("❌ AI分析：页面可能是安全检查页面")
                        return ""
                else:
                    print(f"MCP浏览器访问失败: {result.error_message if hasattr(result, 'error_message') else 'Unknown error'}")
                    return ""

        except Exception as e:
            print(f"MCP浏览器方法失败: {str(e)}")
            return ""
    
    async def _method_direct_access(self, url: str) -> str:
        """方法4: 直接访问 - 使用正确的crawl4ai异步上下文管理器"""
        try:
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(url=url)

                if result.success and result.html:
                    print(f"直接访问成功，页面长度: {len(result.html)}")
                    return result.html
                else:
                    print(f"直接访问失败: {result.error_message if hasattr(result, 'error_message') else 'Unknown error'}")
                    return ""

        except Exception as e:
            print(f"直接访问失败: {str(e)}")
            return ""
    
    async def _method_with_referrer(self, url: str) -> str:
        """方法5: 带Referrer访问"""
        try:
            if not self.crawler:
                browser_config = BrowserConfig(
                    headless=True,
                    browser_type="chromium",
                    viewport_width=1920,
                    viewport_height=1080,
                    user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
                )
                self.crawler = AsyncWebCrawler(config=browser_config)
                await self.crawler.astart()
            
            headers = {
                "Referer": "https://www.zhipin.com/",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
            }
            
            result = await self.crawler.arun(url=url, headers=headers)
            
            if result.success and result.html:
                return result.html
            else:
                return ""
                
        except Exception as e:
            return ""
    
    async def _method_mobile_ua(self, url: str) -> str:
        """方法6: 移动端User-Agent"""
        try:
            if not self.crawler:
                browser_config = BrowserConfig(
                    headless=True,
                    browser_type="chromium",
                    viewport_width=375,
                    viewport_height=667,
                    user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15"
                )
                self.crawler = AsyncWebCrawler(config=browser_config)
                await self.crawler.astart()
            
            result = await self.crawler.arun(url=url)
            
            if result.success and result.html:
                return result.html
            else:
                return ""
                
        except Exception as e:
            return ""
    
    async def _method_api_approach(self, url: str) -> str:
        """方法7: API方式"""
        try:
            if not self.crawler:
                browser_config = BrowserConfig(
                    headless=True,
                    browser_type="chromium"
                )
                self.crawler = AsyncWebCrawler(config=browser_config)
                await self.crawler.astart()
            
            # 尝试访问可能的API端点
            api_url = url.replace("/web/geek/jobs", "/wapi/zpgeek/job/detail.json")
            
            result = await self.crawler.arun(url=api_url)
            
            if result.success and result.html:
                return result.html
            else:
                return ""
                
        except Exception as e:
            return ""
    
    def is_security_check_page(self, html_content: str) -> bool:
        """检查是否是安全检查页面"""
        if not html_content:
            return True

        security_indicators = [
            "请稍候",
            "正在加载中",
            "安全验证",
            "人机验证",
            "verify-slider",
            "网站访客身份验证"
        ]

        return any(indicator in html_content for indicator in security_indicators)

    def _ai_analyze_page_content(self, page_snapshot: str) -> bool:
        """使用AI分析页面内容，判断是否包含有效的职位信息"""
        try:
            # AI分析页面内容的关键指标
            job_indicators = [
                "职位", "岗位", "招聘", "工作", "薪资", "工资",
                "公司", "企业", "技能要求", "工作经验",
                "job", "position", "salary", "company",
                "job-list", "job-item", "position-list"
            ]

            security_indicators = [
                "安全验证", "人机验证", "请稍候", "正在加载",
                "verify", "captcha", "security", "loading",
                "请开启JavaScript", "请升级浏览器"
            ]

            # 统计职位相关指标
            job_score = sum(1 for indicator in job_indicators if indicator.lower() in page_snapshot.lower())

            # 统计安全检查指标
            security_score = sum(1 for indicator in security_indicators if indicator.lower() in page_snapshot.lower())

            print(f"🤖 AI分析结果: 职位指标={job_score}, 安全检查指标={security_score}")

            # AI判断逻辑：职位指标多且安全检查指标少
            if job_score >= 3 and security_score <= 1:
                return True
            elif job_score >= 5:  # 职位指标很多时，即使有少量安全检查指标也认为是正常页面
                return True
            else:
                return False

        except Exception as e:
            print(f"AI页面分析失败: {str(e)}")
            return False

# 全局实例
boss_crawler = BOSSCrawler()
