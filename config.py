"""
BOSS直聘爬虫配置文件
高性能反反爬配置
"""

import random

# 基础配置
BASE_URL = "https://www.zhipin.com"
SEARCH_URL = "https://www.zhipin.com/web/geek/jobs"

# 性能配置
MAX_CONCURRENT_REQUESTS = 20  # 最大并发请求数
REQUEST_DELAY_RANGE = (0.5, 2.0)  # 请求延时范围（秒）
RETRY_TIMES = 3  # 重试次数
TIMEOUT = 30  # 请求超时时间

# User-Agent池（轮换使用）
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0",
]

# 请求头模板
HEADERS_TEMPLATES = [
    {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
    },
    {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Upgrade-Insecure-Requests": "1",
    }
]

# CSS选择器配置（用于数据提取）
SELECTORS = {
    # 职位列表页选择器
    "job_list": "li[data-jobid]",
    "job_link": "a[href*='/job_detail/']",
    "next_page": "a[ka='page-next']",
    
    # 职位详情页选择器
    "job_title": "h1.job-title",
    "salary": ".job-primary .salary",
    "benefits": ".job-tags .tag-list span",
    "job_description": ".job-detail-section .text",
    "company_intro": ".company-info .company-text",
    "work_location": ".location-address",
    "company_name": ".company-info .company-name a",
}

# 输出配置
OUTPUT_DIR = "output"
JSON_FILENAME = "boss_jobs.json"
EXCEL_FILENAME = "boss_jobs.xlsx"

# 城市代码映射（扩展版 - 覆盖更多城市）
CITY_CODES = {
    # 一线城市
    "北京": "*********",
    "上海": "*********",
    "广州": "*********",
    "深圳": "*********",

    # 新一线城市
    "杭州": "*********",
    "南京": "*********",
    "武汉": "*********",
    "成都": "*********",
    "西安": "*********",
    "重庆": "*********",
    "天津": "*********",
    "苏州": "*********",
    "长沙": "*********",
    "沈阳": "*********",
    "青岛": "*********",
    "郑州": "*********",
    "大连": "*********",
    "东莞": "*********",
    "宁波": "*********",

    # 二线城市
    "厦门": "*********",
    "福州": "*********",
    "无锡": "*********",
    "合肥": "*********",
    "昆明": "*********",
    "哈尔滨": "*********",
    "济南": "*********",
    "佛山": "*********",
    "长春": "101060100",
    "温州": "101210300",
    "石家庄": "101090100",
    "南宁": "101300100",
    "常州": "101190300",
    "泉州": "101230500",
    "南昌": "101240100",
    "贵阳": "101260100",
    "太原": "101100100",
    "烟台": "101120500",
    "嘉兴": "101210500",
}

# 职位分类代码（热门职位）
POSITION_CODES = {
    "技术": "100000",
    "产品": "110000", 
    "设计": "120000",
    "运营": "130000",
    "市场": "140000",
    "销售": "140000",
    "职能": "150000",
    "金融": "180000",
    "教育": "190000",
}

def get_random_user_agent():
    """获取随机User-Agent"""
    return random.choice(USER_AGENTS)

def get_random_headers():
    """获取随机请求头"""
    headers = random.choice(HEADERS_TEMPLATES).copy()
    headers["User-Agent"] = get_random_user_agent()
    return headers

# URL爬取相关配置
URL_CRAWL_CONFIG = {
    # 并发控制
    "max_concurrent_cities": 3,  # 最大并发城市数
    "max_concurrent_positions": 2,  # 最大并发职位分类数
    "max_pages_per_search": 50,  # 每个搜索的最大页数

    # 延时配置
    "city_delay_range": (3.0, 6.0),  # 城市间延时范围（秒）
    "position_delay_range": (2.0, 4.0),  # 职位分类间延时范围（秒）
    "page_delay_range": (1.5, 3.0),  # 页面间延时范围（秒）

    # 重试配置
    "max_retries": 3,  # 最大重试次数
    "retry_delay": 2.0,  # 重试延时

    # 输出配置
    "url_output_dir": "output",  # URL输出目录
    "url_filename_prefix": "boss_job_urls",  # URL文件名前缀
    "statistics_filename_prefix": "url_statistics",  # 统计文件名前缀
}

# 搜索URL模板
SEARCH_URL_TEMPLATE = "https://www.zhipin.com/web/geek/jobs?city={city_code}&position={position_code}&page={page}"

# URL验证正则表达式
URL_VALIDATION_PATTERNS = {
    "job_detail": r"^https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html$",
    "job_list": r"^https://www\.zhipin\.com/web/geek/jobs",
}

def get_random_delay():
    """获取随机延时"""
    return random.uniform(*REQUEST_DELAY_RANGE)
