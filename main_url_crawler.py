"""
BOSS直聘URL爬虫主程序
基于现有架构的全站URL发现系统
"""

import asyncio
import sys
import os
from typing import List, Dict
from boss_url_crawler import boss_url_crawler
from url_storage import url_storage
from config import CITY_CODES, POSITION_CODES

def print_banner():
    """打印程序横幅"""
    print("🚀 BOSS直聘全站URL爬虫系统")
    print("=" * 60)
    print("✅ 基于现有爬虫架构")
    print("✅ 全地区全职位覆盖")
    print("✅ 智能反爬虫绕过")
    print("✅ URL结果保存为txt格式")
    print("=" * 60)

def print_usage():
    """打印使用说明"""
    print("📋 使用方法:")
    print("  python main_url_crawler.py                    # 交互式模式")
    print("  python main_url_crawler.py --all              # 爬取全国所有城市")
    print("  python main_url_crawler.py --cities 北京,上海   # 爬取指定城市")
    print("  python main_url_crawler.py --help             # 显示帮助")
    print("")

def print_available_cities():
    """打印可用城市列表"""
    print("🏙️ 可用城市列表:")
    cities = list(CITY_CODES.keys())
    for i, city in enumerate(cities, 1):
        print(f"  {i:2d}. {city}")
    print(f"\n总计: {len(cities)} 个城市")

def print_available_positions():
    """打印可用职位分类"""
    print("📋 可用职位分类:")
    for i, (pos_name, pos_code) in enumerate(POSITION_CODES.items(), 1):
        print(f"  {i}. {pos_name}")
    print(f"\n总计: {len(POSITION_CODES)} 个分类")

async def interactive_mode():
    """交互式模式"""
    print("🎯 交互式模式")
    print("-" * 40)

    while True:
        print("\n请选择操作:")
        print("1. 爬取全国所有城市")
        print("2. 爬取指定城市")
        print("3. 查看可用城市列表")
        print("4. 查看可用职位分类")
        print("5. 退出")

        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == "1":
            await crawl_all_cities()
        elif choice == "2":
            await crawl_selected_cities()
        elif choice == "3":
            print_available_cities()
        elif choice == "4":
            print_available_positions()
        elif choice == "5":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

async def crawl_all_cities():
    """爬取全国所有城市"""
    print("\n🌍 准备爬取全国所有城市的职位URL")
    print(f"📊 将爬取 {len(CITY_CODES)} 个城市")

    confirm = input("确认开始？这可能需要较长时间 (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 已取消")
        return

    # 启用实时保存
    url_storage.enable_realtime_save(batch_size=5)  # 每5个URL保存一次

    print("\n🚀 开始全国爬取...")
    print("💾 实时保存已启用，每发现5个URL自动保存一次")

    try:
        async with boss_url_crawler:
            all_urls = await boss_url_crawler.discover_all_urls()

            # 保存结果
            await save_results(all_urls, "全国")

    except Exception as e:
        print(f"❌ 爬取过程中出现异常: {str(e)}")

async def crawl_selected_cities():
    """爬取指定城市"""
    print("\n🏙️ 选择要爬取的城市")
    print_available_cities()
    
    city_input = input("\n请输入城市名称（多个城市用逗号分隔）: ").strip()
    
    if not city_input:
        print("❌ 未输入城市名称")
        return
    
    # 解析城市列表
    selected_cities = [city.strip() for city in city_input.split(',')]
    valid_cities = []
    
    for city in selected_cities:
        if city in CITY_CODES:
            valid_cities.append(city)
        else:
            print(f"⚠️ 跳过未知城市: {city}")
    
    if not valid_cities:
        print("❌ 没有有效的城市")
        return
    
    print(f"\n📊 将爬取以下城市: {', '.join(valid_cities)}")
    
    confirm = input("确认开始？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 已取消")
        return

    # 启用实时保存
    url_storage.enable_realtime_save(batch_size=5)  # 每5个URL保存一次

    print(f"\n🚀 开始爬取 {len(valid_cities)} 个城市...")
    print("💾 实时保存已启用，每发现5个URL自动保存一次")

    try:
        async with boss_url_crawler:
            all_urls = await boss_url_crawler.discover_all_urls(cities=valid_cities)

            # 保存结果
            await save_results(all_urls, f"{len(valid_cities)}个城市")

    except Exception as e:
        print(f"❌ 爬取过程中出现异常: {str(e)}")

async def save_results(all_urls: Dict[str, List[str]], scope: str):
    """保存爬取结果"""
    print(f"\n💾 保存爬取结果...")
    
    # 保存URL列表
    url_file = url_storage.save_to_txt()
    
    # 保存统计信息
    stats_file = url_storage.save_statistics()
    
    # 保存详细结果
    detail_file = save_detailed_results(all_urls, scope)
    
    print(f"\n✅ 爬取完成！")
    print(f"📁 文件保存位置:")
    if url_file:
        print(f"  URL列表: {url_file}")
    if stats_file:
        print(f"  统计报告: {stats_file}")
    if detail_file:
        print(f"  详细结果: {detail_file}")

def save_detailed_results(all_urls: Dict[str, List[str]], scope: str) -> str:
    """保存详细结果"""
    try:
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"detailed_results_{timestamp}.txt"
        filepath = os.path.join(url_storage.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"BOSS直聘URL爬取详细结果\n")
            f.write(f"爬取范围: {scope}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")
            
            total_urls = 0
            for city, urls in all_urls.items():
                f.write(f"🏙️ {city}: {len(urls)} 个职位\n")
                total_urls += len(urls)
                
                # 写入前10个URL作为示例
                for i, url in enumerate(urls[:10], 1):
                    f.write(f"  {i}. {url}\n")
                
                if len(urls) > 10:
                    f.write(f"  ... 还有 {len(urls) - 10} 个URL\n")
                
                f.write("\n")
            
            f.write(f"总计: {total_urls} 个职位URL\n")
        
        return filepath
        
    except Exception as e:
        print(f"❌ 保存详细结果失败: {str(e)}")
        return ""

async def main():
    """主函数"""
    print_banner()

    # 检查命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1]

        if arg == "--help" or arg == "-h":
            print_usage()
            return
        elif arg == "--all":
            print("🌍 命令行模式：爬取全国所有城市")
            try:
                async with boss_url_crawler:
                    all_urls = await boss_url_crawler.discover_all_urls()
                    await save_results(all_urls, "全国")
            except Exception as e:
                print(f"❌ 爬取异常: {str(e)}")
            return
        elif arg.startswith("--cities="):
            cities_str = arg.split("=", 1)[1]
            selected_cities = [city.strip() for city in cities_str.split(',')]
            valid_cities = [city for city in selected_cities if city in CITY_CODES]
            
            if not valid_cities:
                print("❌ 没有有效的城市")
                return
            
            print(f"🏙️ 命令行模式：爬取指定城市 {', '.join(valid_cities)}")
            try:
                async with boss_url_crawler:
                    all_urls = await boss_url_crawler.discover_all_urls(cities=valid_cities)
                    await save_results(all_urls, f"{len(valid_cities)}个城市")
            except Exception as e:
                print(f"❌ 爬取异常: {str(e)}")
            return
        else:
            print(f"❌ 未知参数: {arg}")
            print_usage()
            return

    # 交互式模式
    await interactive_mode()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 程序已中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
