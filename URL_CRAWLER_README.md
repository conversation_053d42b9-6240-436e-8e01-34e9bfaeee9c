# BOSS直聘全站URL爬虫

🚀 **基于现有架构的BOSS直聘全站URL发现系统**

## ✨ 新增功能

### 🎯 核心能力
- **全站URL发现** - 自动发现BOSS直聘全部地理区域的全部招聘岗位URL
- **智能反爬虫** - 复用现有项目的多重反爬虫绕过机制
- **全地区覆盖** - 支持40+个主要城市的全覆盖爬取
- **多职位分类** - 覆盖技术、产品、设计、运营等9大职位分类
- **txt格式输出** - 将发现的URL保存为txt格式文件

### 🏗️ 技术架构
- **继承现有能力** - 基于`BOSSCrawler`类，复用所有安全绕过机制
- **模块化设计** - 新增3个核心模块，与现有架构完美集成
- **异步并发** - 保持现有的高性能异步处理能力
- **智能去重** - 自动去重和URL验证

## 📁 新增文件

### 核心模块
- `boss_url_crawler.py` - URL发现爬虫（继承现有BOSSCrawler）
- `url_storage.py` - URL存储和管理模块
- `main_url_crawler.py` - URL爬虫主程序
- `test_url_crawler.py` - 测试脚本

### 配置扩展
- `config.py` - 扩展了城市代码和URL爬取配置

## 🚀 快速使用

### 1. 交互式模式
```bash
python main_url_crawler.py
```

选择操作：
- 爬取全国所有城市
- 爬取指定城市
- 查看可用城市列表

### 2. 命令行模式

**爬取全国所有城市：**
```bash
python main_url_crawler.py --all
```

**爬取指定城市：**
```bash
python main_url_crawler.py --cities=北京,上海,深圳
```

### 3. 测试功能
```bash
# 运行所有测试
python test_url_crawler.py

# 测试单个城市
python test_url_crawler.py single

# 测试多个城市
python test_url_crawler.py multiple
```

## 📊 支持的城市

### 一线城市
北京、上海、广州、深圳

### 新一线城市
杭州、南京、武汉、成都、西安、重庆、天津、苏州、长沙、沈阳、青岛、郑州、大连、东莞、宁波

### 二线城市
厦门、福州、无锡、合肥、昆明、哈尔滨、济南、佛山、长春、温州、石家庄、南宁、常州、泉州、南昌、贵阳、太原、烟台、嘉兴

**总计：40+ 个城市**

## 📋 支持的职位分类

- 技术
- 产品
- 设计
- 运营
- 市场
- 销售
- 职能
- 金融
- 教育

## 📄 输出文件

### URL列表文件
```
# BOSS直聘职位URL列表
# 生成时间: 2025-01-02 15:30:45
# 总URL数量: 15420
# 覆盖城市: 10
#
https://www.zhipin.com/job_detail/abc123.html
https://www.zhipin.com/job_detail/def456.html
...
```

### 统计报告文件
```
BOSS直聘URL爬取统计报告
==================================================

📊 基本统计:
  总处理URL数: 15500
  有效URL数: 15420
  重复URL数: 65
  无效URL数: 15
  有效率: 99.5%

🏙️ 城市分布:
  北京: 3245 个职位
  上海: 2890 个职位
  深圳: 2156 个职位
  ...
```

## ⚙️ 配置说明

### 并发控制
```python
URL_CRAWL_CONFIG = {
    "max_concurrent_cities": 3,      # 最大并发城市数
    "max_concurrent_positions": 2,   # 最大并发职位分类数
    "max_pages_per_search": 50,      # 每个搜索的最大页数
}
```

### 延时配置
```python
"city_delay_range": (3.0, 6.0),      # 城市间延时范围（秒）
"position_delay_range": (2.0, 4.0),  # 职位分类间延时范围（秒）
"page_delay_range": (1.5, 3.0),      # 页面间延时范围（秒）
```

## 🔧 技术特点

### 1. 复用现有架构
- 继承`BOSSCrawler`的所有安全绕过能力
- 使用现有的`crawl4ai`框架
- 保持现有的异步处理模式

### 2. 智能URL发现
- 基于城市代码和职位分类的全覆盖搜索
- 自动分页处理
- 智能去重和验证

### 3. 反爬虫机制
- 复用`boss_security_bypass.py`的多重策略
- 随机延时和请求头
- 智能重试机制

### 4. 数据处理
- URL格式验证
- 自动去重
- 统计信息生成
- 多格式输出

## 📈 性能指标

### 预期性能
- **覆盖范围**: 40+ 城市 × 9 职位分类 = 360+ 搜索组合
- **预估URL数量**: 50,000+ 个职位URL
- **爬取时间**: 全国爬取约2-4小时（取决于网络和反爬虫情况）
- **成功率**: 95%+ （基于现有爬虫的高成功率）

### 资源消耗
- **内存使用**: 适中（URL去重需要内存）
- **网络请求**: 大量（建议在网络条件良好时运行）
- **存储空间**: 少量（主要是文本文件）

## ⚠️ 注意事项

1. **合规使用** - 仅用于学习和研究目的，请遵守网站使用协议
2. **请求频率** - 已设置合理的请求间隔，避免对服务器造成压力
3. **网络环境** - 建议在稳定的网络环境下运行
4. **运行时间** - 全国爬取需要较长时间，建议选择合适的时间运行
5. **存储空间** - 确保有足够的磁盘空间存储结果文件

## 🔍 故障排除

### 常见问题

**Q: 爬取过程中出现大量失败**
A: 检查网络连接，可能遇到反爬虫限制，建议增加延时或稍后重试

**Q: 某些城市没有发现URL**
A: 可能该城市职位较少或遇到访问限制，属于正常情况

**Q: 程序运行缓慢**
A: 为了避免反爬虫检测，程序设置了合理的延时，这是正常现象

### 调试模式
```bash
# 测试单个城市以验证功能
python test_url_crawler.py single

# 查看详细错误信息
python main_url_crawler.py --cities=北京
```

## 🎯 使用建议

1. **首次使用** - 建议先测试单个城市，验证功能正常
2. **分批爬取** - 可以分批爬取不同城市，避免一次性爬取过多
3. **定期更新** - 职位信息更新频繁，建议定期重新爬取
4. **结果验证** - 爬取完成后检查URL的有效性

## 📞 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 依赖包是否正确安装
3. 配置文件是否正确
4. 查看错误日志信息
