"""
BOSS直聘URL存储处理器
专门处理URL的存储、去重和统计
"""

import os
import time
from typing import List, Set, Dict, Any
from datetime import datetime
from urllib.parse import urlparse, parse_qs

class URLStorage:
    """URL存储处理器"""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        self.ensure_output_dir()
        self.stored_urls: Set[str] = set()
        self.url_stats = {
            'total_urls': 0,
            'unique_urls': 0,
            'duplicate_urls': 0,
            'invalid_urls': 0,
            'cities': {},
            'start_time': None,
            'end_time': None
        }
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def validate_url(self, url: str) -> bool:
        """验证URL是否为有效的BOSS直聘职位URL"""
        if not url or not isinstance(url, str):
            return False
        
        # 检查URL格式
        if not url.startswith("https://www.zhipin.com/job_detail/"):
            return False
        
        # 检查URL结构
        try:
            parsed = urlparse(url)
            if not parsed.path or not parsed.path.endswith('.html'):
                return False
            return True
        except Exception:
            return False
    
    def extract_city_from_url(self, url: str) -> str:
        """从URL中提取城市信息（如果可能）"""
        try:
            # 这里可以根据需要实现城市提取逻辑
            # 目前返回默认值
            return "未知城市"
        except Exception:
            return "未知城市"
    
    def add_url(self, url: str, city: str = None) -> bool:
        """添加URL到存储中"""
        self.url_stats['total_urls'] += 1
        
        # 验证URL
        if not self.validate_url(url):
            self.url_stats['invalid_urls'] += 1
            return False
        
        # 检查重复
        if url in self.stored_urls:
            self.url_stats['duplicate_urls'] += 1
            return False
        
        # 添加URL
        self.stored_urls.add(url)
        self.url_stats['unique_urls'] += 1
        
        # 统计城市信息
        if not city:
            city = self.extract_city_from_url(url)
        
        if city not in self.url_stats['cities']:
            self.url_stats['cities'][city] = 0
        self.url_stats['cities'][city] += 1
        
        return True
    
    def add_urls_batch(self, urls: List[str], city: str = None) -> Dict[str, int]:
        """批量添加URL"""
        result = {
            'added': 0,
            'duplicates': 0,
            'invalid': 0
        }
        
        for url in urls:
            if self.add_url(url, city):
                result['added'] += 1
            elif not self.validate_url(url):
                result['invalid'] += 1
            else:
                result['duplicates'] += 1
        
        return result
    
    def save_to_txt(self, filename: str = None) -> str:
        """保存URL到txt文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"boss_job_urls_{timestamp}.txt"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                # 写入文件头信息
                f.write(f"# BOSS直聘职位URL列表\n")
                f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 总URL数量: {len(self.stored_urls)}\n")
                f.write(f"# 覆盖城市: {len(self.url_stats['cities'])}\n")
                f.write(f"#\n")
                
                # 写入URL列表
                for url in sorted(self.stored_urls):
                    f.write(f"{url}\n")
            
            print(f"✅ URL列表已保存到: {filepath}")
            print(f"📊 总计: {len(self.stored_urls)} 个有效URL")
            return filepath
            
        except Exception as e:
            print(f"❌ 保存URL文件失败: {str(e)}")
            return ""
    
    def save_statistics(self, filename: str = None) -> str:
        """保存统计信息"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"url_statistics_{timestamp}.txt"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("BOSS直聘URL爬取统计报告\n")
                f.write("=" * 50 + "\n\n")
                
                # 基本统计
                f.write("📊 基本统计:\n")
                f.write(f"  总处理URL数: {self.url_stats['total_urls']}\n")
                f.write(f"  有效URL数: {self.url_stats['unique_urls']}\n")
                f.write(f"  重复URL数: {self.url_stats['duplicate_urls']}\n")
                f.write(f"  无效URL数: {self.url_stats['invalid_urls']}\n")
                
                if self.url_stats['total_urls'] > 0:
                    success_rate = (self.url_stats['unique_urls'] / self.url_stats['total_urls']) * 100
                    f.write(f"  有效率: {success_rate:.1f}%\n")
                
                f.write("\n")
                
                # 城市分布
                if self.url_stats['cities']:
                    f.write("🏙️ 城市分布:\n")
                    sorted_cities = sorted(self.url_stats['cities'].items(), 
                                         key=lambda x: x[1], reverse=True)
                    for city, count in sorted_cities:
                        f.write(f"  {city}: {count} 个职位\n")
                
                f.write("\n")
                
                # 时间信息
                if self.url_stats['start_time']:
                    f.write("⏱️ 时间信息:\n")
                    f.write(f"  开始时间: {self.url_stats['start_time']}\n")
                    if self.url_stats['end_time']:
                        f.write(f"  结束时间: {self.url_stats['end_time']}\n")
                        duration = (self.url_stats['end_time'] - self.url_stats['start_time']).total_seconds()
                        f.write(f"  总耗时: {duration:.1f} 秒\n")
            
            print(f"📈 统计报告已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ 保存统计文件失败: {str(e)}")
            return ""
    
    def start_timing(self):
        """开始计时"""
        self.url_stats['start_time'] = datetime.now()
    
    def end_timing(self):
        """结束计时"""
        self.url_stats['end_time'] = datetime.now()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.url_stats.copy()
    
    def print_statistics(self):
        """打印统计信息"""
        print("\n📊 URL爬取统计:")
        print(f"  总处理: {self.url_stats['total_urls']} 个")
        print(f"  有效: {self.url_stats['unique_urls']} 个")
        print(f"  重复: {self.url_stats['duplicate_urls']} 个")
        print(f"  无效: {self.url_stats['invalid_urls']} 个")
        
        if self.url_stats['total_urls'] > 0:
            success_rate = (self.url_stats['unique_urls'] / self.url_stats['total_urls']) * 100
            print(f"  有效率: {success_rate:.1f}%")
        
        if self.url_stats['cities']:
            print(f"  覆盖城市: {len(self.url_stats['cities'])} 个")
    
    def clear(self):
        """清空存储的URL"""
        self.stored_urls.clear()
        self.url_stats = {
            'total_urls': 0,
            'unique_urls': 0,
            'duplicate_urls': 0,
            'invalid_urls': 0,
            'cities': {},
            'start_time': None,
            'end_time': None
        }

# 全局实例
url_storage = URLStorage()
