"""
BOSS直聘URL爬虫演示版本
基于现有成功页面展示完整的URL发现和存储功能
"""

import asyncio
import os
from boss_url_crawler import boss_url_crawler
from url_storage import url_storage

async def demo_url_extraction():
    """演示URL提取功能"""
    print("🚀 BOSS直聘URL爬虫演示")
    print("=" * 60)
    print("📋 基于现有项目的核心爬虫技术")
    print("📋 演示全站URL发现和存储功能")
    print("=" * 60)
    
    # 清空之前的数据
    url_storage.clear()
    url_storage.start_timing()
    
    # 使用现有的成功页面进行演示
    demo_files = [
        ("success_page_1.html", "北京技术岗位"),
        ("success_page_4.html", "北京其他岗位"),
    ]
    
    total_urls = 0
    
    for filename, description in demo_files:
        if not os.path.exists(filename):
            print(f"⚠️ 演示文件不存在: {filename}")
            continue
        
        print(f"\n🏙️ 处理: {description}")
        print(f"📄 文件: {filename}")
        
        try:
            # 读取页面内容
            with open(filename, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            print(f"  页面大小: {len(html_content):,} 字符")
            
            # 验证页面
            is_valid = boss_url_crawler.validate_search_page(html_content)
            print(f"  页面验证: {'✅ 有效职位列表页面' if is_valid else '❌ 无效页面'}")
            
            if not is_valid:
                continue
            
            # 提取URL
            print(f"  🔍 开始提取职位URL...")
            urls = boss_url_crawler.extract_job_urls_from_html(html_content)
            
            if urls:
                print(f"  ✅ 成功提取 {len(urls)} 个职位URL")
                
                # 添加到存储
                city_name = description.split("技术")[0] if "技术" in description else description.split("其他")[0]
                result = url_storage.add_urls_batch(urls, city_name)
                
                print(f"  📊 存储结果:")
                print(f"    新增: {result['added']} 个")
                print(f"    重复: {result['duplicates']} 个")
                print(f"    无效: {result['invalid']} 个")
                
                total_urls += len(urls)
                
                # 显示示例URL
                print(f"  📋 示例URL:")
                for i, url in enumerate(urls[:3], 1):
                    print(f"    {i}. {url}")
                
                if len(urls) > 3:
                    print(f"    ... 还有 {len(urls) - 3} 个URL")
            else:
                print(f"  ❌ 未能提取到URL")
        
        except Exception as e:
            print(f"  ❌ 处理失败: {str(e)}")
    
    # 结束计时
    url_storage.end_timing()
    
    # 显示最终统计
    print(f"\n" + "=" * 60)
    print("📊 演示完成统计")
    print("=" * 60)
    
    stats = url_storage.get_statistics()
    print(f"总处理URL: {stats['total_urls']}")
    print(f"有效URL: {stats['unique_urls']}")
    print(f"重复URL: {stats['duplicate_urls']}")
    print(f"无效URL: {stats['invalid_urls']}")
    
    if stats['total_urls'] > 0:
        success_rate = (stats['unique_urls'] / stats['total_urls']) * 100
        print(f"有效率: {success_rate:.1f}%")
    
    if stats['cities']:
        print(f"覆盖城市: {len(stats['cities'])} 个")
        for city, count in stats['cities'].items():
            print(f"  {city}: {count} 个职位")
    
    # 保存结果
    print(f"\n💾 保存演示结果...")
    
    url_file = url_storage.save_to_txt("demo_boss_urls.txt")
    stats_file = url_storage.save_statistics("demo_statistics.txt")
    
    print(f"✅ 演示完成！")
    print(f"📁 结果文件:")
    if url_file:
        print(f"  URL列表: {url_file}")
    if stats_file:
        print(f"  统计报告: {stats_file}")
    
    return stats['unique_urls'] > 0

async def demo_full_workflow():
    """演示完整的工作流程"""
    print("\n🎯 完整工作流程演示")
    print("=" * 60)
    
    print("1️⃣ 初始化URL存储系统")
    url_storage.clear()
    print("   ✅ 存储系统已清空并初始化")
    
    print("\n2️⃣ 模拟城市遍历过程")
    demo_cities = ["北京", "上海", "深圳"]
    
    for city in demo_cities:
        print(f"\n🏙️ 模拟爬取城市: {city}")
        
        # 模拟生成一些示例URL
        demo_urls = [
            f"https://www.zhipin.com/job_detail/demo_{city}_tech_{i}.html"
            for i in range(1, 6)
        ]
        
        print(f"  📋 模拟发现 {len(demo_urls)} 个职位URL")
        
        # 添加到存储
        result = url_storage.add_urls_batch(demo_urls, city)
        print(f"  ✅ 存储完成: 新增 {result['added']} 个URL")
    
    print("\n3️⃣ 数据验证和统计")
    url_storage.print_statistics()
    
    print("\n4️⃣ 结果输出")
    url_file = url_storage.save_to_txt("demo_workflow_urls.txt")
    stats_file = url_storage.save_statistics("demo_workflow_stats.txt")
    
    print(f"✅ 工作流程演示完成！")
    print(f"📁 输出文件:")
    print(f"  URL列表: {url_file}")
    print(f"  统计报告: {stats_file}")

async def demo_architecture_showcase():
    """展示技术架构特点"""
    print("\n🏗️ 技术架构展示")
    print("=" * 60)
    
    print("📋 核心技术特点:")
    print("  ✅ 基于现有BOSSCrawler架构")
    print("  ✅ 复用多重反爬虫绕过机制")
    print("  ✅ 异步并发处理能力")
    print("  ✅ 智能URL验证和去重")
    print("  ✅ 多格式数据输出")
    
    print("\n📋 支持的功能范围:")
    print(f"  🏙️ 支持城市: 40+ 个主要城市")
    print(f"  📊 职位分类: 9 大类别")
    print(f"  🔍 预估覆盖: 50,000+ 职位URL")
    print(f"  💾 输出格式: txt格式文件")
    
    print("\n📋 质量保证:")
    print("  ✅ URL格式验证")
    print("  ✅ 自动去重处理")
    print("  ✅ 详细统计报告")
    print("  ✅ 错误处理和恢复")

async def main():
    """主演示函数"""
    print("🚀 BOSS直聘全站URL爬虫系统演示")
    print("基于Position_url_crawler项目的核心技术架构")
    print("=" * 80)
    
    demos = [
        ("URL提取功能演示", demo_url_extraction),
        ("完整工作流程演示", demo_full_workflow),
        ("技术架构展示", demo_architecture_showcase),
    ]
    
    for demo_name, demo_func in demos:
        print(f"\n🎬 {demo_name}")
        try:
            await demo_func()
            print(f"✅ {demo_name} 完成")
        except Exception as e:
            print(f"❌ {demo_name} 失败: {str(e)}")
    
    print(f"\n" + "=" * 80)
    print("🎉 演示完成！")
    print("=" * 80)
    print("📋 实际使用方法:")
    print("  python main_url_crawler.py              # 交互式模式")
    print("  python main_url_crawler.py --all        # 爬取全国")
    print("  python main_url_crawler.py --cities=北京,上海  # 指定城市")
    print("")
    print("📋 注意事项:")
    print("  • 实际爬取时可能遇到安全验证，这是正常现象")
    print("  • 建议在网络条件良好时运行")
    print("  • 全国爬取需要较长时间，请耐心等待")
    print("  • 结果文件保存在output目录中")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 演示已中断")
    except Exception as e:
        print(f"\n❌ 演示异常: {str(e)}")
