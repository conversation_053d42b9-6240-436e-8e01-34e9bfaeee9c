"""
BOSS直聘URL爬虫测试脚本
用于验证URL爬虫功能
"""

import asyncio
import sys
from boss_url_crawler import boss_url_crawler
from url_storage import url_storage

async def test_single_city():
    """测试单个城市爬取"""
    print("🧪 测试单个城市URL爬取")
    print("=" * 40)
    
    test_city = "北京"
    print(f"测试城市: {test_city}")
    
    try:
        async with boss_url_crawler:
            # 测试单个城市
            all_urls = await boss_url_crawler.discover_all_urls(cities=[test_city])
            
            if test_city in all_urls:
                urls = all_urls[test_city]
                print(f"✅ 成功发现 {len(urls)} 个URL")
                
                # 显示前5个URL作为示例
                print("\n📋 示例URL:")
                for i, url in enumerate(urls[:5], 1):
                    print(f"  {i}. {url}")
                
                if len(urls) > 5:
                    print(f"  ... 还有 {len(urls) - 5} 个URL")
                
                # 保存结果
                url_file = url_storage.save_to_txt(f"test_{test_city}_urls.txt")
                stats_file = url_storage.save_statistics(f"test_{test_city}_stats.txt")
                
                print(f"\n💾 测试结果已保存:")
                print(f"  URL文件: {url_file}")
                print(f"  统计文件: {stats_file}")
                
                return True
            else:
                print(f"❌ 未能获取到 {test_city} 的URL")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

async def test_multiple_cities():
    """测试多个城市爬取"""
    print("\n🧪 测试多个城市URL爬取")
    print("=" * 40)
    
    test_cities = ["北京", "上海"]
    print(f"测试城市: {', '.join(test_cities)}")
    
    try:
        # 清空之前的数据
        url_storage.clear()
        
        async with boss_url_crawler:
            all_urls = await boss_url_crawler.discover_all_urls(cities=test_cities)
            
            total_urls = 0
            for city in test_cities:
                if city in all_urls:
                    urls = all_urls[city]
                    total_urls += len(urls)
                    print(f"✅ {city}: {len(urls)} 个URL")
                else:
                    print(f"❌ {city}: 未获取到URL")
            
            print(f"\n📊 总计: {total_urls} 个URL")
            
            # 保存结果
            url_file = url_storage.save_to_txt("test_multiple_cities_urls.txt")
            stats_file = url_storage.save_statistics("test_multiple_cities_stats.txt")
            
            print(f"\n💾 测试结果已保存:")
            print(f"  URL文件: {url_file}")
            print(f"  统计文件: {stats_file}")
            
            return total_urls > 0
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

async def test_url_validation():
    """测试URL验证功能"""
    print("\n🧪 测试URL验证功能")
    print("=" * 40)
    
    test_urls = [
        "https://www.zhipin.com/job_detail/abc123.html",  # 有效
        "https://www.zhipin.com/job_detail/xyz789.html",  # 有效
        "https://www.zhipin.com/company/123",  # 无效
        "https://example.com/job.html",  # 无效
        "",  # 无效
        None,  # 无效
    ]
    
    valid_count = 0
    invalid_count = 0
    
    for url in test_urls:
        is_valid = url_storage.validate_url(url)
        status = "✅ 有效" if is_valid else "❌ 无效"
        print(f"  {status}: {url}")
        
        if is_valid:
            valid_count += 1
        else:
            invalid_count += 1
    
    print(f"\n📊 验证结果:")
    print(f"  有效URL: {valid_count}")
    print(f"  无效URL: {invalid_count}")
    
    return True

async def test_url_storage():
    """测试URL存储功能"""
    print("\n🧪 测试URL存储功能")
    print("=" * 40)
    
    # 清空存储
    url_storage.clear()
    
    # 测试URL
    test_urls = [
        "https://www.zhipin.com/job_detail/test1.html",
        "https://www.zhipin.com/job_detail/test2.html",
        "https://www.zhipin.com/job_detail/test1.html",  # 重复
        "https://www.zhipin.com/job_detail/test3.html",
        "invalid_url",  # 无效
    ]
    
    print("添加测试URL...")
    for url in test_urls:
        result = url_storage.add_url(url, "测试城市")
        status = "✅ 已添加" if result else "❌ 跳过"
        print(f"  {status}: {url}")
    
    # 打印统计
    url_storage.print_statistics()
    
    # 保存测试结果
    url_file = url_storage.save_to_txt("test_storage.txt")
    stats_file = url_storage.save_statistics("test_storage_stats.txt")
    
    print(f"\n💾 测试结果已保存:")
    print(f"  URL文件: {url_file}")
    print(f"  统计文件: {stats_file}")
    
    return True

async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始BOSS直聘URL爬虫测试")
    print("=" * 60)
    
    tests = [
        ("URL验证功能", test_url_validation),
        ("URL存储功能", test_url_storage),
        ("单个城市爬取", test_single_city),
        ("多个城市爬取", test_multiple_cities),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = await test_func()
            if result:
                print(f"✅ 测试通过: {test_name}")
                passed += 1
            else:
                print(f"❌ 测试失败: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ 测试异常: {test_name} - {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")

async def main():
    """主函数"""
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        
        if test_type == "single":
            await test_single_city()
        elif test_type == "multiple":
            await test_multiple_cities()
        elif test_type == "validation":
            await test_url_validation()
        elif test_type == "storage":
            await test_url_storage()
        elif test_type == "all":
            await run_all_tests()
        else:
            print("❌ 未知测试类型")
            print("可用测试: single, multiple, validation, storage, all")
    else:
        await run_all_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {str(e)}")
