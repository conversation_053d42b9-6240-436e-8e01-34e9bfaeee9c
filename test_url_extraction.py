"""
URL提取功能测试脚本
专门测试从HTML页面中提取职位URL的功能
"""

import asyncio
from boss_url_crawler import boss_url_crawler
from url_storage import url_storage

async def test_url_extraction_with_known_page():
    """使用已知的成功页面测试URL提取"""
    print("🧪 测试URL提取功能")
    print("=" * 50)
    
    # 使用现有项目中的成功页面进行测试
    test_files = [
        "success_page_1.html",
        "success_page_4.html"
    ]
    
    for test_file in test_files:
        try:
            print(f"\n📄 测试文件: {test_file}")
            
            # 读取测试文件
            with open(test_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            print(f"  页面长度: {len(html_content)}")
            
            # 验证页面
            is_valid = boss_url_crawler.validate_search_page(html_content)
            print(f"  页面验证: {'✅ 有效' if is_valid else '❌ 无效'}")
            
            # 提取URL
            urls = boss_url_crawler.extract_job_urls_from_html(html_content)
            print(f"  提取结果: {len(urls)} 个URL")
            
            if urls:
                print(f"  示例URL:")
                for i, url in enumerate(urls[:5], 1):
                    print(f"    {i}. {url}")
                
                # 验证URL格式
                valid_urls = [url for url in urls if boss_url_crawler.is_valid_job_url(url)]
                print(f"  有效URL: {len(valid_urls)}/{len(urls)}")
            
        except FileNotFoundError:
            print(f"  ⚠️ 文件不存在: {test_file}")
        except Exception as e:
            print(f"  ❌ 测试失败: {str(e)}")

async def test_single_search_page():
    """测试单个搜索页面的爬取和URL提取"""
    print("\n🧪 测试单个搜索页面")
    print("=" * 50)
    
    # 使用一个简单的搜索URL
    search_url = "https://www.zhipin.com/web/geek/jobs?city=101010100&position=100000&page=1"
    
    try:
        async with boss_url_crawler:
            print(f"🔍 测试URL: {search_url}")
            
            # 尝试获取页面内容
            html_content = await boss_url_crawler.crawl_search_page(search_url)
            
            if html_content:
                print(f"✅ 成功获取页面，长度: {len(html_content)}")
                
                # 提取URL
                urls = boss_url_crawler.extract_job_urls_from_html(html_content)
                
                if urls:
                    print(f"🎉 成功提取到 {len(urls)} 个URL!")
                    
                    # 保存结果
                    url_storage.clear()
                    url_storage.add_urls_batch(urls, "测试")
                    
                    # 保存到文件
                    url_file = url_storage.save_to_txt("test_extracted_urls.txt")
                    print(f"💾 URL已保存到: {url_file}")
                    
                    return True
                else:
                    print("❌ 未能提取到URL")
                    return False
            else:
                print("❌ 未能获取页面内容")
                return False
                
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

async def test_url_validation():
    """测试URL验证功能"""
    print("\n🧪 测试URL验证功能")
    print("=" * 50)
    
    test_urls = [
        "https://www.zhipin.com/job_detail/abc123def456.html",  # 有效
        "https://www.zhipin.com/job_detail/xyz789.html",       # 有效
        "https://www.zhipin.com/job_detail/test.html",         # 有效
        "https://www.zhipin.com/company/123",                  # 无效
        "https://example.com/job.html",                        # 无效
        "/job_detail/relative.html",                           # 无效（相对路径）
        "",                                                    # 无效
        None,                                                  # 无效
    ]
    
    valid_count = 0
    for url in test_urls:
        is_valid = boss_url_crawler.is_valid_job_url(url)
        status = "✅ 有效" if is_valid else "❌ 无效"
        print(f"  {status}: {url}")
        if is_valid:
            valid_count += 1
    
    print(f"\n📊 验证结果: {valid_count}/{len(test_urls)} 个URL有效")
    return True

async def main():
    """主测试函数"""
    print("🚀 URL提取功能测试套件")
    print("=" * 60)
    
    tests = [
        ("URL验证功能", test_url_validation),
        ("已知页面URL提取", test_url_extraction_with_known_page),
        ("实时搜索页面测试", test_single_search_page),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = await test_func()
            if result:
                print(f"✅ 测试通过: {test_name}")
                passed += 1
            else:
                print(f"❌ 测试失败: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ 测试异常: {test_name} - {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {str(e)}")
